// ============================================================================
// TEST 1-SECOND BACKTESTER WITH FIXES
// ============================================================================
// Quick test to verify the 1-second backtester is working after fixes

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

function log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`);
}

async function runTest() {
    log('🚀 Testing 1-Second Backtester with fixes...');
    
    // Set environment variables for a quick test
    const env = {
        ...process.env,
        OHLCV_1SEC_DIR: 'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
        OUT_DIR: './test_1sec_results',
        SL_RANGE: '6',  // Test just one SL
        TP_RANGE: '8'   // Test just one TP
    };
    
    // Ensure output directory exists
    if (!fs.existsSync('./test_1sec_results')) {
        fs.mkdirSync('./test_1sec_results', { recursive: true });
    }
    
    return new Promise((resolve, reject) => {
        log('📊 Starting backtester...');
        
        const child = spawn('node', ['ohlcv_1sec_backtester.js'], {
            env,
            stdio: 'pipe'
        });
        
        let output = '';
        let errorOutput = '';
        
        child.stdout.on('data', (data) => {
            const text = data.toString();
            output += text;
            
            // Log important lines
            const lines = text.split('\n');
            for (const line of lines) {
                if (line.includes('DEBUG') || 
                    line.includes('Found') || 
                    line.includes('Built') || 
                    line.includes('generated') ||
                    line.includes('Complete') ||
                    line.includes('trades') ||
                    line.includes('ERROR') ||
                    line.includes('TRADE_')) {
                    console.log(line);
                }
            }
        });
        
        child.stderr.on('data', (data) => {
            errorOutput += data.toString();
            console.error('STDERR:', data.toString());
        });
        
        child.on('close', (code) => {
            log(`Backtester finished with code: ${code}`);
            
            if (code === 0) {
                // Check results
                checkResults();
                resolve({ code, output, errorOutput });
            } else {
                reject(new Error(`Process failed with code ${code}: ${errorOutput}`));
            }
        });
        
        // Kill after 2 minutes if still running
        setTimeout(() => {
            if (!child.killed) {
                log('⏰ Killing process after 2 minutes...');
                child.kill();
                resolve({ code: 'TIMEOUT', output, errorOutput });
            }
        }, 120000);
    });
}

function checkResults() {
    log('📋 Checking results...');
    
    const resultsDir = './test_1sec_results';
    
    try {
        // Check summaries
        const summariesDir = path.join(resultsDir, 'summaries');
        if (fs.existsSync(summariesDir)) {
            const summaryFiles = fs.readdirSync(summariesDir);
            log(`📊 Found ${summaryFiles.length} summary files`);
            
            for (const file of summaryFiles) {
                if (file.endsWith('.json')) {
                    const summaryPath = path.join(summariesDir, file);
                    const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
                    log(`   📈 ${file}: ${summary.totalTrades} trades, ${summary.winRate?.toFixed(1)}% WR, $${summary.totalPnL?.toFixed(2)} P&L`);
                }
            }
        }
        
        // Check trades
        const tradesDir = path.join(resultsDir, 'trades');
        if (fs.existsSync(tradesDir)) {
            const tradeDirs = fs.readdirSync(tradesDir);
            log(`📁 Found ${tradeDirs.length} trade directories`);
            
            for (const dir of tradeDirs) {
                const tradePath = path.join(tradesDir, dir);
                if (fs.statSync(tradePath).isDirectory()) {
                    const tradeFiles = fs.readdirSync(tradePath);
                    log(`   📂 ${dir}: ${tradeFiles.length} trade files`);
                }
            }
        }
        
        // Check progress
        const progressDir = path.join(resultsDir, 'progress');
        if (fs.existsSync(progressDir)) {
            const debugLog = path.join(progressDir, 'debug.log');
            if (fs.existsSync(debugLog)) {
                const logContent = fs.readFileSync(debugLog, 'utf8');
                const lines = logContent.split('\n');
                log(`📝 Debug log has ${lines.length} lines`);
                
                // Show last few lines
                const lastLines = lines.slice(-10).filter(l => l.trim());
                for (const line of lastLines) {
                    if (line.includes('DEBUG') || line.includes('Complete')) {
                        log(`   ${line}`);
                    }
                }
            }
        }
        
    } catch (error) {
        log(`❌ Error checking results: ${error.message}`);
    }
}

async function main() {
    try {
        const result = await runTest();
        
        if (result.code === 0) {
            log('✅ Test completed successfully!');
        } else if (result.code === 'TIMEOUT') {
            log('⏰ Test timed out - checking partial results...');
            checkResults();
        } else {
            log(`❌ Test failed with code: ${result.code}`);
        }
        
    } catch (error) {
        log(`❌ Test error: ${error.message}`);
    }
}

if (require.main === module) {
    main();
}
