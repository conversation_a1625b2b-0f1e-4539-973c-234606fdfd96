// ============================================================================
// DEBUG 1-SECOND DATA ISSUES
// ============================================================================
// Quick diagnostic to understand what's wrong with 1-second data processing

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CONFIG = {
    // Try different possible data directories
    POSSIBLE_DIRS: [
        'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
        'D:\\backtest-output\\1secdata_jan2020_aug2025',
        'G:\\ohlcv_1sec',
        'input\\1sec_data',
        '.'
    ],
    
    // Test with a recent date that should exist
    TEST_DATE: '20240815', // August 15, 2024
    
    DEBUG: true
};

function log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`);
}

function findDataDirectory() {
    log('🔍 Searching for 1-second data directory...');
    
    for (const dir of CONFIG.POSSIBLE_DIRS) {
        if (fs.existsSync(dir)) {
            log(`✅ Found directory: ${dir}`);
            
            // List files in directory
            try {
                const files = fs.readdirSync(dir);
                const ohlcvFiles = files.filter(f => f.includes('ohlcv-1s') && f.includes('.zst'));
                log(`   📁 Contains ${files.length} total files, ${ohlcvFiles.length} 1-second OHLCV files`);
                
                if (ohlcvFiles.length > 0) {
                    log(`   📄 Sample files: ${ohlcvFiles.slice(0, 3).join(', ')}`);
                    return { dir, files: ohlcvFiles };
                }
            } catch (error) {
                log(`   ❌ Error reading directory: ${error.message}`);
            }
        } else {
            log(`❌ Directory not found: ${dir}`);
        }
    }
    
    return null;
}

function decompressFile(filePath) {
    try {
        log(`🗜️ Decompressing: ${filePath}`);
        const result = execSync(`zstd -d "${filePath}" -c`, { 
            encoding: 'utf8', 
            maxBuffer: 1024 * 1024 * 10 // 10MB buffer for testing
        });
        log(`✅ Decompressed successfully, ${result.length} characters`);
        return result;
    } catch (error) {
        log(`❌ Error decompressing ${filePath}: ${error.message}`);
        return null;
    }
}

function analyzeCSVFormat(csvData, fileName) {
    log(`📊 Analyzing CSV format for: ${fileName}`);
    
    const lines = csvData.trim().split('\n');
    const header = lines[0];
    
    log(`   📋 Header: ${header}`);
    log(`   📏 Total lines: ${lines.length}`);
    
    if (lines.length < 2) {
        log(`   ❌ No data lines found`);
        return null;
    }
    
    // Analyze first few data lines
    for (let i = 1; i <= Math.min(5, lines.length - 1); i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        const parts = line.split(',');
        log(`   📝 Line ${i}: ${parts.length} columns`);
        log(`      🕐 Timestamp: ${parts[0]}`);
        log(`      💰 OHLCV: ${parts[4]}, ${parts[5]}, ${parts[6]}, ${parts[7]}, ${parts[8]}`);
        log(`      🏷️ Symbol (pos 9): ${parts[9] || 'N/A'}`);
        log(`      🏷️ Last column: ${parts[parts.length - 1]}`);
        
        // Try to find MNQ symbol
        const mnqColumns = parts.filter((col, idx) => col && col.includes('MNQ'));
        if (mnqColumns.length > 0) {
            log(`      ✅ Found MNQ symbols: ${mnqColumns.join(', ')}`);
        } else {
            log(`      ❌ No MNQ symbols found in this line`);
        }
    }
    
    return { header, totalLines: lines.length, sampleLines: lines.slice(1, 6) };
}

function testSignalGeneration(minuteCandles) {
    log(`🎯 Testing signal generation with ${minuteCandles.length} minute candles`);
    
    if (minuteCandles.length < 200) {
        log(`   ❌ Not enough candles for WMA200 (need 200, have ${minuteCandles.length})`);
        return [];
    }
    
    // Simple test - just check if we can calculate indicators
    let signalCount = 0;
    
    for (let i = 200; i < Math.min(minuteCandles.length, 250); i++) {
        const current = minuteCandles[i];
        
        // Mock indicators for testing
        current.wma50 = current.close * 0.99;
        current.wma200 = current.close * 0.98;
        current.rsi = 65; // Mock RSI above 60
        
        // Simple bullish test
        if (current.close > current.wma50 && current.wma50 > current.wma200 && current.rsi > 60) {
            signalCount++;
        }
    }
    
    log(`   📈 Generated ${signalCount} potential signals`);
    return signalCount;
}

function buildMinuteCandles(secondData) {
    log(`🕐 Building minute candles from ${secondData.length} second records`);
    
    const minuteCandles = new Map();
    
    for (const tick of secondData) {
        // Round down to minute
        const minuteKey = new Date(tick.timestamp);
        minuteKey.setSeconds(0, 0);
        const key = minuteKey.toISOString();
        
        if (!minuteCandles.has(key)) {
            minuteCandles.set(key, {
                timestamp: minuteKey,
                symbol: tick.symbol,
                open: tick.open,
                high: tick.high,
                low: tick.low,
                close: tick.close,
                volume: tick.volume
            });
        } else {
            const candle = minuteCandles.get(key);
            candle.high = Math.max(candle.high, tick.high);
            candle.low = Math.min(candle.low, tick.low);
            candle.close = tick.close; // Last close in the minute
            candle.volume += tick.volume;
        }
    }
    
    const result = Array.from(minuteCandles.values()).sort((a, b) => a.timestamp - b.timestamp);
    log(`   ✅ Built ${result.length} minute candles`);
    
    if (result.length > 0) {
        log(`   📅 Time range: ${result[0].timestamp.toISOString()} to ${result[result.length-1].timestamp.toISOString()}`);
    }
    
    return result;
}

function parseOHLCV1Sec(csvData) {
    const lines = csvData.trim().split('\n');
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        const parts = line.split(',');
        if (parts.length < 9) continue;
        
        const timestamp = new Date(parts[0]);
        
        // Try different symbol positions
        let symbol = parts[9] || parts[parts.length - 1] || parts[1];
        
        // Filter for MNQ contracts only
        if (!symbol || !symbol.startsWith('MNQ')) continue;
        
        const ohlcvData = {
            timestamp,
            symbol,
            open: parseFloat(parts[4]),
            high: parseFloat(parts[5]),
            low: parseFloat(parts[6]),
            close: parseFloat(parts[7]),
            volume: parseInt(parts[8])
        };
        
        // Validate OHLCV data
        if (isNaN(ohlcvData.open) || isNaN(ohlcvData.high) || isNaN(ohlcvData.low) || isNaN(ohlcvData.close)) {
            continue;
        }
        
        data.push(ohlcvData);
    }
    
    return data;
}

async function main() {
    log('🚀 Starting 1-Second Data Diagnostic...');
    
    // Step 1: Find data directory
    const dataInfo = findDataDirectory();
    if (!dataInfo) {
        log('❌ No 1-second data directory found!');
        return;
    }
    
    // Step 2: Find a test file
    const testFile = dataInfo.files.find(f => f.includes(CONFIG.TEST_DATE));
    if (!testFile) {
        log(`❌ No file found for test date ${CONFIG.TEST_DATE}`);
        log(`Available files: ${dataInfo.files.slice(0, 5).join(', ')}`);
        return;
    }
    
    const testFilePath = path.join(dataInfo.dir, testFile);
    log(`🎯 Testing with file: ${testFilePath}`);
    
    // Step 3: Decompress and analyze
    const csvData = decompressFile(testFilePath);
    if (!csvData) {
        log('❌ Failed to decompress test file');
        return;
    }
    
    // Step 4: Analyze CSV format
    const formatInfo = analyzeCSVFormat(csvData, testFile);
    if (!formatInfo) {
        log('❌ Failed to analyze CSV format');
        return;
    }
    
    // Step 5: Parse 1-second data
    const secondData = parseOHLCV1Sec(csvData);
    log(`📊 Parsed ${secondData.length} valid 1-second records`);
    
    if (secondData.length === 0) {
        log('❌ No valid 1-second data parsed!');
        return;
    }
    
    // Step 6: Build minute candles
    const minuteCandles = buildMinuteCandles(secondData);
    
    // Step 7: Test signal generation
    const signalCount = testSignalGeneration(minuteCandles);
    
    // Step 8: Summary
    log('\n📋 DIAGNOSTIC SUMMARY:');
    log(`   📁 Data directory: ${dataInfo.dir}`);
    log(`   📄 Test file: ${testFile}`);
    log(`   📊 1-second records: ${secondData.length}`);
    log(`   🕐 Minute candles: ${minuteCandles.length}`);
    log(`   🎯 Potential signals: ${signalCount}`);
    
    if (secondData.length > 0 && minuteCandles.length > 0 && signalCount > 0) {
        log('✅ Data processing appears to be working!');
    } else {
        log('❌ Issues found in data processing pipeline');
    }
}

if (require.main === module) {
    main().catch(console.error);
}
