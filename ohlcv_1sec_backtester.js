// ============================================================================
// 1-SECOND OHLCV FRONT-MONTH GRID BACKTESTER - VALIDATED TRADING EDGE
// ============================================================================
// Uses 1-second OHLCV data from Databento for precise SL/TP validation
// Builds 1-minute OHLCV candles from 1-second data for signal generation
// Tests multiple SL/TP combinations with proper front-month contract rollover
//
// ✅ TRADING EDGE - CORRECTED SPECIFICATION:
// • 3-Candle Pattern: Green-Red-Green (bullish) / Red-Green-Red (bearish) - NO BODY VALIDATION
// • MA Confluence: Price > WMA50 > WMA200 (bullish) / Price < WMA50 < WMA200 (bearish)
// • RSI Conditions: RSI > 60 (bullish) / RSI < 40 (bearish) - NO RSI-MA
// • Trading Hours: 7am-3pm CST/CDT (UTC 13-20 standard, 12-19 daylight)
// • Indicators: RSI(14), WMA(50), WMA(200) calculated on 24-hour data
// • Slippage: Entry +0.25, SL +0.5, TP +0.0 points
// • Contract Value: $6 per point for MNQ micro contracts
// ============================================================================

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ============================================================================
// CONFIGURATION
// ============================================================================

const CONFIG = {
    // Data source configuration
    DATA_SOURCE: '1SEC_OHLCV', // 1-second OHLCV from Databento
    OHLCV_1SEC_DIR: process.env.OHLCV_1SEC_DIR || 'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
    OUT_DIR: process.env.OUT_DIR || 'D:\\backtest-output\\1secdata_jan2020_aug2025\\RESULTS',
    
    // Date range - Use actual available data range
    START_DATE: '2024-08-01',
    END_DATE: '2024-08-31', // Test with August 2024 data first
    
    // Grid configuration
    SL_RANGE: process.env.SL_RANGE ? process.env.SL_RANGE.split(',').map(Number) : [5, 6, 7],
    TP_RANGE: process.env.TP_RANGE ? process.env.TP_RANGE.split(',').map(Number) : [15, 18, 20],
    
    // Trading configuration
    SLIPPAGE: {
        ENTRY: 0.25,
        SL: 0.5,
        TP: 0.0
    },
    
    // Time filtering - 7am-3pm CST/CDT (matches proven backtester)
    TRADING_HOURS: {
        // 7am-3pm CST = 13-21 UTC (standard time)
        // 7am-3pm CDT = 12-20 UTC (daylight time)
        STANDARD: [13, 14, 15, 16, 17, 18, 19, 20], // CST
        DAYLIGHT: [12, 13, 14, 15, 16, 17, 18, 19]  // CDT
    },
    
    // Debug
    DEBUG: true
};

// ============================================================================
// CME FRONT-MONTH CONTRACT ROLLOVER DATES
// ============================================================================

const CME_ROLLOVER_DATES = {
    // Monday prior to the 3rd Friday of expiration month
    '2024': {
        'M': '2024-06-17', // June
        'U': '2024-09-16', // September  
        'Z': '2024-12-16'  // December
    },
    '2025': {
        'H': '2025-03-17', // March
        'M': '2025-06-16', // June
        'U': '2025-09-15', // September
        'Z': '2025-12-15'  // December
    }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function log(message) {
    const timestamp = new Date().toISOString();
    console.log(`${timestamp} ${message}`);
    if (CONFIG.DEBUG) {
        fs.appendFileSync(path.join(CONFIG.OUT_DIR, 'progress', 'debug.log'), `${timestamp} ${message}\n`);
    }
}

function ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

function getFrontMonthSymbol(date) {
    // Matches the proven backtester's front-month logic
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 1-12
    const day = date.getDate();

    // CME rollover logic: Monday prior to 3rd Friday of expiration month
    if (year === 2025) {
        if (month <= 3 || (month === 3 && day < 17)) return 'MNQH5'; // Mar 2025
        if (month <= 6 || (month === 6 && day < 16)) return 'MNQM5'; // Jun 2025
        if (month <= 9 || (month === 9 && day < 15)) return 'MNQU5'; // Sep 2025
        return 'MNQZ5'; // Dec 2025
    } else if (year === 2024) {
        if (month <= 3 || (month === 3 && day < 18)) return 'MNQH4'; // Mar 2024
        if (month <= 6 || (month === 6 && day < 17)) return 'MNQM4'; // Jun 2024
        if (month <= 9 || (month === 9 && day < 16)) return 'MNQU4'; // Sep 2024
        return 'MNQZ4'; // Dec 2024
    }

    // Default fallback for other years
    const yearSuffix = year.toString().slice(-1);
    if (month <= 3) return `MNQH${yearSuffix}`;
    if (month <= 6) return `MNQM${yearSuffix}`;
    if (month <= 9) return `MNQU${yearSuffix}`;
    return `MNQZ${yearSuffix}`;
}

function isDaylightSavingTime(date) {
    // US DST: Second Sunday in March to First Sunday in November
    const year = date.getFullYear();
    const march = new Date(year, 2, 1); // March 1st
    const november = new Date(year, 10, 1); // November 1st
    
    // Find second Sunday in March
    const dstStart = new Date(march);
    dstStart.setDate(1 + (7 - march.getDay()) % 7 + 7); // Second Sunday
    
    // Find first Sunday in November  
    const dstEnd = new Date(november);
    dstEnd.setDate(1 + (7 - november.getDay()) % 7); // First Sunday
    
    return date >= dstStart && date < dstEnd;
}

function isInTradingHours(date) {
    const hour = date.getUTCHours();
    const isDST = isDaylightSavingTime(date);
    const validHours = isDST ? CONFIG.TRADING_HOURS.DAYLIGHT : CONFIG.TRADING_HOURS.STANDARD;
    return validHours.includes(hour);
}

// ============================================================================
// DATA PROCESSING FUNCTIONS
// ============================================================================

function decompressFile(filePath) {
    try {
        const result = execSync(`zstd -d "${filePath}" -c`, { encoding: 'utf8', maxBuffer: 1024 * 1024 * 100 });
        return result;
    } catch (error) {
        log(`Error decompressing ${filePath}: ${error.message}`);
        return null;
    }
}

function parseOHLCV1Sec(csvData) {
    const lines = csvData.trim().split('\n');
    const header = lines[0];
    const data = [];

    // Debug: Log header to understand format
    if (CONFIG.DEBUG) {
        log(`CSV Header: ${header}`);
        log(`Total lines: ${lines.length}`);
    }

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(',');

        // Debug: Log first few lines to understand format
        if (CONFIG.DEBUG && i <= 3) {
            log(`Line ${i}: ${parts.length} parts - ${line.substring(0, 100)}...`);
        }

        if (parts.length < 9) {
            if (CONFIG.DEBUG && i <= 10) {
                log(`Skipping line ${i}: only ${parts.length} parts`);
            }
            continue;
        }

        const timestamp = new Date(parts[0]);

        // Symbol is at position 9 (confirmed by diagnostic)
        let symbol = parts[9];

        // Filter for MNQ contracts only
        if (!symbol || !symbol.startsWith('MNQ')) {
            if (CONFIG.DEBUG && i <= 10) {
                log(`Skipping line ${i}: symbol '${symbol}' doesn't start with MNQ`);
            }
            continue;
        }

        const ohlcvData = {
            timestamp,
            symbol,
            open: parseFloat(parts[4]),
            high: parseFloat(parts[5]),
            low: parseFloat(parts[6]),
            close: parseFloat(parts[7]),
            volume: parseInt(parts[8])
        };

        // Validate OHLCV data
        if (isNaN(ohlcvData.open) || isNaN(ohlcvData.high) || isNaN(ohlcvData.low) || isNaN(ohlcvData.close)) {
            if (CONFIG.DEBUG && i <= 10) {
                log(`Skipping line ${i}: invalid OHLCV data`);
            }
            continue;
        }

        data.push(ohlcvData);
    }

    if (CONFIG.DEBUG) {
        log(`Parsed ${data.length} valid 1-second records`);
        if (data.length > 0) {
            log(`First record: ${JSON.stringify(data[0])}`);
            log(`Last record: ${JSON.stringify(data[data.length - 1])}`);
        }
    }

    return data;
}

function buildMinuteCandles(secondData) {
    const minuteCandles = new Map();
    
    for (const tick of secondData) {
        // Round down to minute
        const minuteKey = new Date(tick.timestamp);
        minuteKey.setSeconds(0, 0);
        const key = minuteKey.toISOString();
        
        if (!minuteCandles.has(key)) {
            minuteCandles.set(key, {
                timestamp: minuteKey,
                symbol: tick.symbol,
                open: tick.open,
                high: tick.high,
                low: tick.low,
                close: tick.close,
                volume: tick.volume
            });
        } else {
            const candle = minuteCandles.get(key);
            candle.high = Math.max(candle.high, tick.high);
            candle.low = Math.min(candle.low, tick.low);
            candle.close = tick.close; // Last close in the minute
            candle.volume += tick.volume;
        }
    }
    
    return Array.from(minuteCandles.values()).sort((a, b) => a.timestamp - b.timestamp);
}

// ============================================================================
// MAIN EXECUTION - OPTIMIZED GRID PROCESSING
// ============================================================================

async function main() {
    // Ensure output directories exist first
    ensureDir(CONFIG.OUT_DIR);
    ensureDir(path.join(CONFIG.OUT_DIR, 'progress'));
    ensureDir(path.join(CONFIG.OUT_DIR, 'trades'));
    ensureDir(path.join(CONFIG.OUT_DIR, 'summaries'));

    log('🚀 1-Second OHLCV Front-Month Grid Backtester Starting...');
    log(`📊 Data Source: ${CONFIG.DATA_SOURCE}`);
    log(`📊 Date Range: ${CONFIG.START_DATE} to ${CONFIG.END_DATE}`);
    log(`📊 Grid: SL [${CONFIG.SL_RANGE.join(', ')}] × TP [${CONFIG.TP_RANGE.join(', ')}]`);
    log(`📁 Output -> ${CONFIG.OUT_DIR}`);

    // Get list of files to process
    const startDate = new Date(CONFIG.START_DATE);
    const endDate = new Date(CONFIG.END_DATE);
    const filesToProcess = [];

    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0].replace(/-/g, '');
        const fileName = `glbx-mdp3-${dateStr}.ohlcv-1s.csv.zst`;
        const filePath = path.join(CONFIG.OHLCV_1SEC_DIR, fileName);

        if (fs.existsSync(filePath)) {
            filesToProcess.push({
                date: dateStr,
                path: filePath
            });
        }

        currentDate.setDate(currentDate.getDate() + 1);
    }

    log(`📅 Found ${filesToProcess.length} files to process`);

    // Initialize all combo trackers
    const combos = [];
    for (const sl of CONFIG.SL_RANGE) {
        for (const tp of CONFIG.TP_RANGE) {
            combos.push({
                sl,
                tp,
                name: `SL${sl}_TP${tp}`,
                trades: [],
                currentPosition: null,
                usedSignals: new Set() // Track used signals to prevent re-entry
            });
            ensureDir(path.join(CONFIG.OUT_DIR, 'trades', `SL${sl}_TP${tp}`));
        }
    }

    log(`⚡ Processing ${combos.length} combos simultaneously...`);

    // Process all combos together (MUCH more efficient)
    await runOptimizedGrid(combos, filesToProcess);

    log('🏁 All combinations complete!');
}

// ============================================================================
// OPTIMIZED GRID PROCESSING
// ============================================================================

async function runOptimizedGrid(combos, filesToProcess) {
    for (let i = 0; i < filesToProcess.length; i++) {
        const file = filesToProcess[i];
        const progress = `${i + 1}/${filesToProcess.length}`;
        log(`📊 Day ${progress} -> ${file.date}`);

        // Decompress and parse 1-second data ONCE
        const csvData = decompressFile(file.path);
        if (!csvData) continue;

        const secondData = parseOHLCV1Sec(csvData);
        if (secondData.length === 0) continue;

        // Build 1-minute candles ONCE (all 24 hours for proper indicators)
        const minuteCandles = buildMinuteCandles(secondData);

        // Generate signals ONCE (but filter to trading hours)
        const allSignals = generateSignals(minuteCandles);
        const tradingSignals = allSignals.filter(signal => isInTradingHours(signal.timestamp));

        // Debug logging for first few days
        if (i < 5) {
            log(`DEBUG [${file.date}] Built ${minuteCandles.length} 1-min candles, generated ${allSignals.length} signals, ${tradingSignals.length} in trading hours`);
            if (tradingSignals.length > 0) {
                log(`DEBUG [${file.date}] First signal: ${tradingSignals[0].timestamp.toISOString()} ${tradingSignals[0].direction} at ${tradingSignals[0].price}`);
            }
            if (secondData.length > 0) {
                log(`DEBUG [${file.date}] Second data: ${secondData.length} ticks, first tick: ${secondData[0].timestamp.toISOString()}, symbol: ${secondData[0].symbol}`);
            }
            if (minuteCandles.length > 0) {
                log(`DEBUG [${file.date}] Minute candles: first: ${minuteCandles[0].timestamp.toISOString()}, last: ${minuteCandles[minuteCandles.length-1].timestamp.toISOString()}`);
            }
        }

        // Process all combos simultaneously using same data
        const dayTrades = await processAllCombos(combos, tradingSignals, secondData, file.date);

        // Save daily trades for each combo
        for (const combo of combos) {
            const comboTrades = dayTrades[combo.name] || [];
            if (comboTrades.length > 0) {
                const dailyFile = path.join(CONFIG.OUT_DIR, 'trades', combo.name, `${file.date}.json`);
                fs.writeFileSync(dailyFile, JSON.stringify(comboTrades, null, 2));
            }
            combo.trades.push(...comboTrades);
        }

        // Memory cleanup
        if (i % 50 === 0) {
            log('🧹 Memory optimized');
            global.gc && global.gc();
        }
    }

    // Generate summaries for all combos
    for (const combo of combos) {
        const summary = generateSummary(combo.trades, combo.sl, combo.tp);
        const summaryFile = path.join(CONFIG.OUT_DIR, 'summaries', `${combo.name}.json`);
        fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));

        log(`✅ [${combo.name}] Complete: ${combo.trades.length} trades, ${summary.winRate.toFixed(1)}% WR, $${summary.totalPnL.toFixed(2)} P&L`);
    }
}

async function processAllCombos(combos, signals, secondData, date) {
    const dayTrades = {};

    // Initialize day trades for each combo and reset used signals for new day
    for (const combo of combos) {
        dayTrades[combo.name] = [];
        // Reset used signals for each new day
        combo.usedSignals.clear();
    }

    // Create signal lookup map for fast access (by minute)
    const signalMap = new Map();
    for (const signal of signals) {
        // Use minute-level key (remove seconds)
        const minuteKey = new Date(signal.timestamp);
        minuteKey.setSeconds(0, 0);
        signalMap.set(minuteKey.getTime(), signal);
    }

    // Process EVERY second chronologically (this is the correct approach!)
    for (const tick of secondData) {
        // Skip if not in trading hours
        if (!isInTradingHours(tick.timestamp)) continue;

        // Process each combo independently
        for (const combo of combos) {

            // 1. Check for exits FIRST (if we have a position)
            if (combo.currentPosition) {
                const exitResult = checkExitAtTick(combo.currentPosition, tick);
                if (exitResult) {
                    exitResult.entryTime = combo.currentPosition.entryTime;
                    exitResult.entryPrice = combo.currentPosition.entryPrice;
                    exitResult.direction = combo.currentPosition.direction;
                    dayTrades[combo.name].push(exitResult);
                    combo.currentPosition = null;

                    log(`TRADE_EXIT [${combo.name}] ${exitResult.outcome} at ${exitResult.exitPrice} P&L=$${exitResult.pnl.toFixed(2)}`);
                }
            }

            // 2. Check for new entries (only if no current position)
            // Check if this tick is within the same minute as a signal
            const tickMinute = new Date(tick.timestamp);
            tickMinute.setSeconds(0, 0);
            const minuteKey = tickMinute.getTime();

            if (!combo.currentPosition && signalMap.has(minuteKey)) {
                const signal = signalMap.get(minuteKey);

                // CRITICAL FIX: Only enter once per signal minute
                if (!combo.usedSignals.has(minuteKey)) {
                    combo.usedSignals.add(minuteKey);

                    // Enter new position for this combo
                    const entryPrice = signal.price + (signal.direction === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY);

                    combo.currentPosition = {
                        entryTime: tick.timestamp,
                        entryPrice,
                        direction: signal.direction,
                        sl: signal.direction === 'LONG' ? entryPrice - combo.sl : entryPrice + combo.sl,
                        tp: signal.direction === 'LONG' ? entryPrice + combo.tp : entryPrice - combo.tp
                    };

                    log(`TRADE_ENTRY [${combo.name}] ${signal.direction} signal=${signal.price} fill=${entryPrice} sl=${combo.currentPosition.sl} tp=${combo.currentPosition.tp}`);
                }
            }
        }
    }

    // Check for any remaining open positions at end of day
    for (const combo of combos) {
        if (combo.currentPosition) {
            dayTrades[combo.name].push({
                ...combo.currentPosition,
                outcome: 'OPEN',
                exitTime: null,
                exitPrice: null,
                pnl: 0,
                durationSec: 0
            });
        }
    }

    return dayTrades;
}

// ============================================================================
// LEGACY TRADING LOGIC (UNUSED)
// ============================================================================

async function runCombo(sl, tp, filesToProcess) {
    const comboName = `SL${sl}_TP${tp}`;
    const trades = [];
    let currentPosition = null;

    // Ensure combo directories exist
    ensureDir(path.join(CONFIG.OUT_DIR, 'trades', comboName));

    for (let i = 0; i < filesToProcess.length; i++) {
        const file = filesToProcess[i];
        const progress = `${i + 1}/${filesToProcess.length}`;
        log(`[${comboName}] Day ${progress} -> ${file.date}`);

        // Decompress and parse 1-second data
        const csvData = decompressFile(file.path);
        if (!csvData) continue;

        const secondData = parseOHLCV1Sec(csvData);
        if (secondData.length === 0) continue;

        // Build 1-minute candles for signal generation
        const minuteCandles = buildMinuteCandles(secondData);

        // Process signals and validate with 1-second data
        const dayTrades = await processDay(minuteCandles, secondData, sl, tp, currentPosition);

        // Update current position
        if (dayTrades.length > 0) {
            const lastTrade = dayTrades[dayTrades.length - 1];
            currentPosition = lastTrade.outcome === 'OPEN' ? lastTrade : null;
        }

        // Save daily trades
        if (dayTrades.length > 0) {
            const dailyFile = path.join(CONFIG.OUT_DIR, 'trades', comboName, `${file.date}.json`);
            fs.writeFileSync(dailyFile, JSON.stringify(dayTrades, null, 2));
        }

        trades.push(...dayTrades);
    }

    // Generate summary
    const summary = generateSummary(trades, sl, tp);
    const summaryFile = path.join(CONFIG.OUT_DIR, 'summaries', `${comboName}.json`);
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));

    log(`[${comboName}] Complete: ${trades.length} trades, ${summary.winRate.toFixed(1)}% WR, $${summary.totalPnL.toFixed(2)} P&L`);
}

async function processDay(minuteCandles, secondData, sl, tp, currentPosition) {
    const trades = [];
    const signals = generateSignals(minuteCandles);

    // Create lookup for 1-second data by timestamp
    const secondLookup = new Map();
    for (const tick of secondData) {
        const key = tick.timestamp.toISOString();
        if (!secondLookup.has(key)) {
            secondLookup.set(key, []);
        }
        secondLookup.get(key).push(tick);
    }

    for (const signal of signals) {
        // Skip if not in trading hours
        if (!isInTradingHours(signal.timestamp)) continue;

        // Skip if we already have a position
        if (currentPosition) {
            // Check for exit on 1-second data
            const exitResult = checkExit(currentPosition, secondData, sl, tp, signal.timestamp);
            if (exitResult) {
                exitResult.entryTime = currentPosition.entryTime;
                exitResult.entryPrice = currentPosition.entryPrice;
                exitResult.direction = currentPosition.direction;
                trades.push(exitResult);
                currentPosition = null;
            }
            continue;
        }

        // Enter new position
        const entryPrice = signal.price + (signal.direction === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY);

        currentPosition = {
            entryTime: signal.timestamp,
            entryPrice,
            direction: signal.direction,
            sl: signal.direction === 'LONG' ? entryPrice - sl : entryPrice + sl,
            tp: signal.direction === 'LONG' ? entryPrice + tp : entryPrice - tp
        };

        log(`TRADE_ENTRY ${signal.direction} signal=${signal.price} fill=${entryPrice} sl=${currentPosition.sl} tp=${currentPosition.tp}`);
    }

    // Check if position is still open at end of day
    if (currentPosition) {
        trades.push({
            ...currentPosition,
            outcome: 'OPEN',
            exitTime: null,
            exitPrice: null,
            pnl: 0,
            durationSec: 0
        });
    }

    return trades;
}

// ============================================================================
// SECOND-BY-SECOND EXIT VALIDATION
// ============================================================================

function checkExitAtTick(position, tick) {
    // Only check ticks after entry time
    if (tick.timestamp <= position.entryTime) return null;

    let hitSL = false, hitTP = false;

    // Check if SL or TP was hit using OHLC data from this 1-second tick
    if (position.direction === 'LONG') {
        hitSL = tick.low <= position.sl;
        hitTP = tick.high >= position.tp;
    } else {
        hitSL = tick.high >= position.sl;
        hitTP = tick.low <= position.tp;
    }

    // If both hit in same second, SL takes priority (more conservative)
    if (hitSL || hitTP) {
        const outcome = hitSL ? 'SL' : 'TP';
        const exitPrice = hitSL ? position.sl : position.tp;

        // Apply slippage (matches proven backtester)
        const slippage = outcome === 'SL' ? CONFIG.SLIPPAGE.SL : CONFIG.SLIPPAGE.TP;
        const finalExitPrice = position.direction === 'LONG' ?
            exitPrice - slippage :
            exitPrice + slippage;

        // Calculate P&L ($6 per point for MNQ micro contracts)
        const pnl = position.direction === 'LONG' ?
            (finalExitPrice - position.entryPrice) * 6 :
            (position.entryPrice - finalExitPrice) * 6;

        return {
            outcome,
            exitTime: tick.timestamp,
            exitPrice: finalExitPrice,
            pnl,
            durationSec: Math.floor((tick.timestamp - position.entryTime) / 1000)
        };
    }

    return null;
}

// Legacy function (unused)
function checkExitOptimized(position, secondData, currentTime) {
    // Check 1-second data for precise SL/TP validation
    for (const tick of secondData) {
        // Only check ticks after entry and before current signal time
        if (tick.timestamp <= position.entryTime || tick.timestamp > currentTime) continue;

        // Skip if not in trading hours (exits can only happen during trading)
        if (!isInTradingHours(tick.timestamp)) continue;

        let hitSL = false, hitTP = false;

        // Check if SL or TP was hit using OHLC data
        if (position.direction === 'LONG') {
            hitSL = tick.low <= position.sl;
            hitTP = tick.high >= position.tp;
        } else {
            hitSL = tick.high >= position.sl;
            hitTP = tick.low <= position.tp;
        }

        // If both hit in same candle, SL takes priority (more conservative)
        if (hitSL || hitTP) {
            const outcome = hitSL ? 'SL' : 'TP';
            const exitPrice = hitSL ? position.sl : position.tp;

            // Apply slippage (matches proven backtester)
            const slippage = outcome === 'SL' ? CONFIG.SLIPPAGE.SL : CONFIG.SLIPPAGE.TP;
            const finalExitPrice = position.direction === 'LONG' ?
                exitPrice - slippage :
                exitPrice + slippage;

            // Calculate P&L ($6 per point for MNQ micro contracts)
            const pnl = position.direction === 'LONG' ?
                (finalExitPrice - position.entryPrice) * 6 :
                (position.entryPrice - finalExitPrice) * 6;

            return {
                outcome,
                exitTime: tick.timestamp,
                exitPrice: finalExitPrice,
                pnl,
                durationSec: Math.floor((tick.timestamp - position.entryTime) / 1000)
            };
        }
    }

    return null;
}

// Legacy function (unused)
function checkExit(position, secondData, sl, tp, currentTime) {
    for (const tick of secondData) {
        if (tick.timestamp <= position.entryTime || tick.timestamp > currentTime) continue;

        let hitSL = false, hitTP = false;

        if (position.direction === 'LONG') {
            hitSL = tick.low <= position.sl;
            hitTP = tick.high >= position.tp;
        } else {
            hitSL = tick.high >= position.sl;
            hitTP = tick.low <= position.tp;
        }

        if (hitSL || hitTP) {
            const outcome = hitTP ? 'TP' : 'SL';
            const exitPrice = hitTP ? position.tp : position.sl;
            const slippage = outcome === 'SL' ? CONFIG.SLIPPAGE.SL : CONFIG.SLIPPAGE.TP;
            const finalExitPrice = exitPrice + (position.direction === 'LONG' ? -slippage : slippage);

            const pnl = position.direction === 'LONG' ?
                (finalExitPrice - position.entryPrice) * 6 : // $6 per point for MNQ
                (position.entryPrice - finalExitPrice) * 6;

            return {
                outcome,
                exitTime: tick.timestamp,
                exitPrice: finalExitPrice,
                pnl,
                durationSec: Math.floor((tick.timestamp - position.entryTime) / 1000)
            };
        }
    }

    return null;
}

// ============================================================================
// SIGNAL GENERATION - COMPLETE TRADING EDGE
// ============================================================================

function generateSignals(minuteCandles) {
    if (minuteCandles.length < 200) return []; // Need enough data for WMA200

    // First, calculate indicators for ALL candles (24-hour data for proper context)
    const candlesWithIndicators = calculateIndicators(minuteCandles);

    const signals = [];

    // Generate signals starting from index 200 (need WMA200 history)
    for (let i = 200; i < candlesWithIndicators.length; i++) {
        const current = candlesWithIndicators[i];
        const prev1 = candlesWithIndicators[i - 1];
        const prev2 = candlesWithIndicators[i - 2];

        // Skip if missing indicators
        if (!current.wma50 || !current.wma200 || !current.rsi) continue;

        // MA confluence requirements
        const aboveBothMAs = current.close > current.wma50 && current.wma50 > current.wma200;
        const belowBothMAs = current.close < current.wma50 && current.wma50 < current.wma200;

        // RSI conditions (simple thresholds)
        const rsiAbove60 = current.rsi > 60;
        const rsiBelow40 = current.rsi < 40;

        // 3-candle pattern functions
        const greenCandle = (c) => c.close > c.open;
        const redCandle = (c) => c.close < c.open;

        // SIMPLE 3-candle pattern (no body validation required)
        // Bullish: Green-Red-Green
        const bullishPattern = greenCandle(prev2) && redCandle(prev1) && greenCandle(current);

        // Bearish: Red-Green-Red
        const bearishPattern = redCandle(prev2) && greenCandle(prev1) && redCandle(current);

        // LONG signal: All conditions must be met
        if (aboveBothMAs && rsiAbove60 && bullishPattern) {
            signals.push({
                timestamp: current.timestamp,
                direction: 'LONG',
                price: current.close,
                rsi: current.rsi,
                wma50: current.wma50,
                wma200: current.wma200
            });
        }

        // SHORT signal: All conditions must be met
        if (belowBothMAs && rsiBelow40 && bearishPattern) {
            signals.push({
                timestamp: current.timestamp,
                direction: 'SHORT',
                price: current.close,
                rsi: current.rsi,
                wma50: current.wma50,
                wma200: current.wma200
            });
        }
    }

    return signals;
}

// ============================================================================
// INDICATOR CALCULATIONS - EXACT MATCH TO TICK-GRID BACKTESTER
// ============================================================================

function calculateIndicators(candles) {
    const result = [];

    for (let index = 0; index < candles.length; index++) {
        const candle = candles[index];

        // RSI calculation (14-period)
        let rsi = 50;
        if (index >= 14) {
            const changes = [];
            for (let i = index - 13; i <= index; i++) {
                changes.push(candles[i].close - candles[i-1].close);
            }

            const gains = changes.filter(c => c > 0);
            const losses = changes.filter(c => c < 0).map(c => Math.abs(c));

            const avgGain = gains.length > 0 ? gains.reduce((a, b) => a + b, 0) / 14 : 0;
            const avgLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / 14 : 0;

            if (avgLoss > 0) {
                const rs = avgGain / avgLoss;
                rsi = 100 - (100 / (1 + rs));
            }
        }

        // WMA50 calculation
        let wma50 = candle.close;
        if (index >= 50) {
            let sum = 0, weightSum = 0;
            for (let i = 0; i < 50; i++) {
                const weight = 50 - i;
                sum += candles[index - i].close * weight;
                weightSum += weight;
            }
            wma50 = sum / weightSum;
        }

        // WMA200 calculation
        let wma200 = candle.close;
        if (index >= 200) {
            let sum = 0, weightSum = 0;
            for (let i = 0; i < 200; i++) {
                const weight = 200 - i;
                sum += candles[index - i].close * weight;
                weightSum += weight;
            }
            wma200 = sum / weightSum;
        }

        result.push({
            ...candle,
            rsi,
            wma50,
            wma200
        });
    }

    return result;
}

// ============================================================================
// SUMMARY GENERATION
// ============================================================================

function generateSummary(trades, sl, tp) {
    const completedTrades = trades.filter(t => t.outcome !== 'OPEN');
    const winningTrades = completedTrades.filter(t => t.pnl > 0);

    const totalPnL = completedTrades.reduce((sum, t) => sum + t.pnl, 0);
    const winRate = completedTrades.length > 0 ? (winningTrades.length / completedTrades.length) * 100 : 0;

    const durations = completedTrades.map(t => t.durationSec).filter(d => d > 0);
    const avgDuration = durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;
    const medianDuration = durations.length > 0 ? durations.sort((a, b) => a - b)[Math.floor(durations.length / 2)] : 0;

    // Calculate drawdown
    let runningPnL = 0;
    let peak = 0;
    let maxDrawdown = 0;

    for (const trade of completedTrades) {
        runningPnL += trade.pnl;
        if (runningPnL > peak) {
            peak = runningPnL;
        }
        const drawdown = peak - runningPnL;
        if (drawdown > maxDrawdown) {
            maxDrawdown = drawdown;
        }
    }

    return {
        sl,
        tp,
        totalTrades: completedTrades.length,
        winningTrades: winningTrades.length,
        losingTrades: completedTrades.length - winningTrades.length,
        winRate,
        totalPnL,
        avgDuration,
        medianDuration,
        maxDrawdown,
        expectancy: completedTrades.length > 0 ? totalPnL / completedTrades.length : 0,
        profitFactor: calculateProfitFactor(completedTrades)
    };
}

function calculateProfitFactor(trades) {
    const grossProfit = trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0);
    const grossLoss = Math.abs(trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0));

    return grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
}

if (require.main === module) {
    main().catch(console.error);
}
